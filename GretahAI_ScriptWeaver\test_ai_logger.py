#!/usr/bin/env python
"""
Test script for the enhanced AI logger.

This script demonstrates the enhanced AI logger with function call tracing.
It includes examples of successful API calls, error cases, and cross-referenced logs.
"""

import os
import sys
import time
import json
import traceback
from datetime import datetime

# Add the parent directory to the path so we can import the core module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ai import (
    log_ai_interaction,
    generate_llm_response,
    AI_LOGS_DIR,
    AI_LOGS_REQUESTS_DIR,
    AI_LOGS_ERRORS_DIR,
    AI_LOGS_METRICS_DIR
)
from core.ai_helpers import (
    get_daily_usage_summary,
    generate_usage_report
)

def test_successful_api_call():
    """Test a successful API call with function call tracing."""
    print("\n=== Testing Successful API Call ===")

    # Define a test prompt
    prompt = "Hello, AI! Please generate a short poem about debugging."

    # Call the API
    try:
        response = generate_llm_response(
            prompt=prompt,
            category="test_successful_call"
        )

        print(f"Response received: {response[:50]}...")
        print("Check the logs directory for the log file.")

        return True
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return False

def test_error_case():
    """Test an error case with function call tracing."""
    print("\n=== Testing Error Case ===")

    # Define a function that will cause an error
    def process_data(data_list, index):
        """Process data at the specified index."""
        # This will cause an IndexError if index is out of range
        return data_list[index]

    # Call the function with an invalid index
    try:
        data = [1, 2, 3, 4, 5]
        result = process_data(data, 10)  # Index out of range
    except Exception as e:
        # Log the error
        filepath, request_id = log_ai_interaction(
            function_name="test_error_case",
            prompt="This is a test prompt for an error case",
            response=f"Error: {str(e)}",
            model_name="N/A",
            category="test_error",
            error=e,
            capture_stack=True
        )

        print(f"Error logged: {e}")
        print(f"Log file: {filepath}")

        return True

    print("No error occurred (unexpected)")
    return False

def test_cross_referenced_logs():
    """Test cross-referenced logs."""
    print("\n=== Testing Cross-Referenced Logs ===")

    # First log: Generate a prompt
    prompt_filepath, prompt_request_id = log_ai_interaction(
        function_name="generate_test_prompt",
        prompt="",
        response="Generated prompt: What is the capital of France?",
        model_name="N/A",
        category="test_prompt_generation"
    )

    print(f"Prompt log created: {prompt_filepath}")
    print(f"Prompt request ID: {prompt_request_id}")

    # Second log: Use the prompt to call the API
    try:
        # Simulate an API call
        time.sleep(1)  # Simulate API latency

        # Log the API call, referencing the prompt log
        response_filepath, response_request_id = log_ai_interaction(
            function_name="test_api_call",
            prompt="What is the capital of France?",
            response="The capital of France is Paris.",
            model_name="gemini-1.5-flash",
            category="test_api_response",
            related_request_ids=[prompt_request_id]
        )

        print(f"Response log created: {response_filepath}")
        print(f"Response request ID: {response_request_id}")

        # Third log: Process the response
        result_filepath, result_request_id = log_ai_interaction(
            function_name="process_test_response",
            prompt="",
            response="Processed response: Paris",
            model_name="N/A",
            category="test_response_processing",
            parent_request_id=response_request_id
        )

        print(f"Result log created: {result_filepath}")
        print(f"Result request ID: {result_request_id}")

        return True
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return False

def test_prompt_generation_tracing():
    """Test prompt generation tracing."""
    print("\n=== Testing Prompt Generation Tracing ===")

    # Define template variables
    template_variables = {
        'test_case_id': 'TC_001',
        'step_no': '2',
        'action': 'click',
        'element': 'login_button'
    }

    # Define test data
    test_data = {
        'username': 'testuser',
        'password': 'password123'
    }

    # Define a function that simulates prompt generation
    def generate_test_script_prompt(test_case, step_no, action, element, test_data):
        """Generate a test script prompt."""
        # This is a simplified version of the actual function
        prompt = f"""
        Generate a PyTest script for test case {test_case} step {step_no}.

        Action: {action} on element {element}

        Test Data:
        Username: {test_data['username']}
        Password: {test_data['password']}

        Generate a script that performs this action.
        """
        return prompt

    try:
        # Generate the prompt
        prompt = generate_test_script_prompt(
            template_variables['test_case_id'],
            template_variables['step_no'],
            template_variables['action'],
            template_variables['element'],
            test_data
        )

        # Log the prompt generation with enhanced prompt tracing
        prompt_filepath, prompt_request_id = log_ai_interaction(
            function_name="generate_test_script_prompt",
            prompt=prompt,
            response="Prompt generated successfully",
            model_name="N/A",
            category="test_prompt_generation",
            capture_stack=True,
            is_prompt_generation=True,  # Enable prompt generation tracing
            context={
                'test_case_id': template_variables['test_case_id'],
                'step_no': template_variables['step_no'],
                'action': template_variables['action'],
                'element': template_variables['element'],
                'template_variables': template_variables,
                'test_data': test_data
            }
        )

        print(f"Prompt generation log created: {prompt_filepath}")
        print(f"Prompt generation request ID: {prompt_request_id}")

        # Log an AI call that uses the generated prompt
        response_filepath, response_request_id = log_ai_interaction(
            function_name="generate_llm_response",
            prompt=prompt,
            response="def test_login(browser):\n    # Click login button\n    browser.find_element(By.ID, 'login_button').click()",
            model_name="gemini-1.5-flash",
            category="test_script_generation",
            related_request_ids=[prompt_request_id],  # Link to the prompt log
            context={
                'test_case_id': template_variables['test_case_id'],
                'step_no': template_variables['step_no'],
                'prompt_request_id': prompt_request_id
            }
        )

        print(f"Script generation log created: {response_filepath}")
        print(f"Script generation request ID: {response_request_id}")

        return True
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return False

def test_nested_function_calls():
    """Test nested function calls with function call tracing."""
    print("\n=== Testing Nested Function Calls ===")

    def level3_function(param1, param2):
        """Level 3 function that will be logged."""
        # Log the function call
        filepath, request_id = log_ai_interaction(
            function_name="level3_function",
            prompt=f"Level 3 function called with params: {param1}, {param2}",
            response="Level 3 function response",
            model_name="N/A",
            category="test_nested_calls",
            capture_stack=True
        )

        print(f"Level 3 log created: {filepath}")
        return filepath

    def level2_function(param):
        """Level 2 function that calls level 3."""
        print(f"Level 2 function called with param: {param}")
        return level3_function(param, "level2_param")

    def level1_function():
        """Level 1 function that calls level 2."""
        print("Level 1 function called")
        return level2_function("level1_param")

    try:
        filepath = level1_function()
        print(f"Check the log file for the nested function calls: {filepath}")
        return True
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return False

def test_usage_report():
    """Test generating a usage report."""
    print("\n=== Testing Usage Report ===")

    try:
        # Get today's date
        today = datetime.now().strftime("%Y%m%d")

        # Generate a daily usage summary
        summary = get_daily_usage_summary(today)
        print(f"Daily usage summary: {json.dumps(summary, indent=2)[:200]}...")

        # Generate a usage report
        report = generate_usage_report(
            start_date=today,
            end_date=today,
            output_format="text"
        )
        print(f"Usage report: {report[:200]}...")

        return True
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main function to run all tests."""
    print("=== AI Logger Test Script ===")
    print(f"Logs directory: {AI_LOGS_DIR}")

    # Create the logs directories if they don't exist
    for directory in [AI_LOGS_DIR, AI_LOGS_REQUESTS_DIR, AI_LOGS_ERRORS_DIR, AI_LOGS_METRICS_DIR]:
        os.makedirs(directory, exist_ok=True)

    # Run the tests
    tests = [
        test_successful_api_call,
        test_error_case,
        test_cross_referenced_logs,
        test_prompt_generation_tracing,  # Add the new test
        test_nested_function_calls,
        test_usage_report
    ]

    results = {}
    for test in tests:
        print(f"\nRunning test: {test.__name__}")
        try:
            result = test()
            results[test.__name__] = "PASS" if result else "FAIL"
        except Exception as e:
            print(f"Test failed with exception: {e}")
            traceback.print_exc()
            results[test.__name__] = "ERROR"

    # Print the results
    print("\n=== Test Results ===")
    for test_name, result in results.items():
        print(f"{test_name}: {result}")

if __name__ == "__main__":
    main()
